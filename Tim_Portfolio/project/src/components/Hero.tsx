import React, { useEffect, useState } from 'react';
import { Shield, Code, Server, Award } from 'lucide-react';

const Hero: React.FC = () => {
  const [animatedStats, setAnimatedStats] = useState({
    experience: 0,
    certifications: 0,
    technologies: 0,
    projects: 0
  });

  const finalStats = {
    experience: 8,
    certifications: 12,
    technologies: 25,
    projects: 50
  };

  useEffect(() => {
    const duration = 2000; // 2 seconds
    const steps = 60;
    const interval = duration / steps;

    let step = 0;
    const timer = setInterval(() => {
      step++;
      const progress = step / steps;
      
      setAnimatedStats({
        experience: Math.floor(finalStats.experience * progress),
        certifications: Math.floor(finalStats.certifications * progress),
        technologies: Math.floor(finalStats.technologies * progress),
        projects: Math.floor(finalStats.projects * progress)
      });

      if (step >= steps) {
        clearInterval(timer);
        setAnimatedStats(finalStats);
      }
    }, interval);

    return () => clearInterval(timer);
  }, []);

  const stats = [
    { icon: Award, label: 'Years Experience', value: animatedStats.experience },
    { icon: Shield, label: 'Certifications', value: animatedStats.certifications },
    { icon: Code, label: 'Technologies', value: animatedStats.technologies },
    { icon: Server, label: 'Projects Completed', value: animatedStats.projects }
  ];

  return (
    <section id="home" className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-20 w-32 h-32 border-2 border-blue-500 rotate-45"></div>
        <div className="absolute top-40 right-32 w-24 h-24 border-2 border-green-500 rotate-12"></div>
        <div className="absolute bottom-32 left-32 w-40 h-40 border-2 border-purple-500 rotate-45"></div>
        <div className="absolute bottom-20 right-20 w-28 h-28 border-2 border-yellow-500 rotate-12"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <div className="animate-fade-in-up">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Tim Illguth
          </h1>
          <p className="text-xl sm:text-2xl lg:text-3xl text-gray-700 mb-4">
            Fullstack Blockchain Developer & Network Engineer
          </p>
          <p className="text-lg sm:text-xl text-gray-600 mb-12 max-w-3xl mx-auto">
            Certified Blockchain Developer | Linux Engineer | Network Security Specialist
            <br />
            Building secure, scalable solutions for the decentralized future
          </p>

          {/* Animated Stats */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 max-w-4xl mx-auto">
            {stats.map((stat, index) => (
              <div 
                key={stat.label}
                className="bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex justify-center mb-4">
                  <stat.icon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">
                  {stat.value}+
                </div>
                <div className="text-sm text-gray-600 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>

          {/* CTA Button */}
          <div className="mt-12">
            <button
              onClick={() => document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl"
            >
              View My Work
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;