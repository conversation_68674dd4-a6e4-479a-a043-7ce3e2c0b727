@tailwind base;
@tailwind components;
@tailwind utilities;

/* Northern Lights Animation */
@keyframes aurora {
  0% {
    transform: translateX(-100%) skewX(-15deg);
    opacity: 0;
  }
  25% {
    opacity: 1;
  }
  50% {
    transform: translateX(100%) skewX(15deg);
    opacity: 0.8;
  }
  75% {
    opacity: 0.6;
  }
  100% {
    transform: translateX(200%) skewX(-15deg);
    opacity: 0;
  }
}

@keyframes aurora-2 {
  0% {
    transform: translateX(-150%) skewX(10deg);
    opacity: 0;
  }
  30% {
    opacity: 0.7;
  }
  60% {
    transform: translateX(150%) skewX(-10deg);
    opacity: 0.5;
  }
  100% {
    transform: translateX(250%) skewX(10deg);
    opacity: 0;
  }
}

@keyframes aurora-3 {
  0% {
    transform: translateX(-200%) skewX(-20deg);
    opacity: 0;
  }
  20% {
    opacity: 0.4;
  }
  40% {
    transform: translateX(50%) skewX(20deg);
    opacity: 0.8;
  }
  80% {
    opacity: 0.3;
  }
  100% {
    transform: translateX(300%) skewX(-20deg);
    opacity: 0;
  }
}

@keyframes stars-twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.northern-lights {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: linear-gradient(180deg, #0f0f23 0%, #1a1a2e 30%, #16213e 60%, #0f3460 100%);
}

.aurora-layer {
  position: absolute;
  width: 200%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(0, 255, 150, 0.3) 25%,
    rgba(0, 200, 255, 0.4) 50%,
    rgba(150, 0, 255, 0.3) 75%,
    transparent 100%
  );
  animation: aurora 45s infinite linear;
}

.aurora-layer-2 {
  position: absolute;
  width: 180%;
  height: 80%;
  top: 10%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 100, 200, 0.2) 30%,
    rgba(100, 255, 200, 0.3) 60%,
    transparent 100%
  );
  animation: aurora-2 60s infinite linear;
  animation-delay: -5s;
}

.aurora-layer-3 {
  position: absolute;
  width: 220%;
  height: 60%;
  top: 20%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 200, 100, 0.15) 20%,
    rgba(200, 100, 255, 0.25) 40%,
    rgba(100, 200, 255, 0.2) 80%,
    transparent 100%
  );
  animation: aurora-3 75s infinite linear;
  animation-delay: -10s;
}

.stars {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, #eee, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, #fff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
    radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: stars-twinkle 8s ease-in-out infinite alternate;
}

.profile-picture-glow {
  box-shadow:
    0 0 20px rgba(0, 255, 150, 0.3),
    0 0 40px rgba(0, 200, 255, 0.2),
    0 0 60px rgba(150, 0, 255, 0.1);
}

/* Fade in animation */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 1s ease-out;
}

/* Custom animations */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.dark ::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: #4b5563;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Code syntax highlighting improvements */
pre code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.6;
}

/* Enhanced focus states for accessibility */
button:focus,
input:focus,
textarea:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.dark button:focus,
.dark input:focus,
.dark textarea:focus {
  outline: 2px solid #60a5fa;
  outline-offset: 2px;
}

/* Improved card hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Loading animation for stats */
@keyframes counter {
  from {
    opacity: 0.3;
  }
  to {
    opacity: 1;
  }
}

.stat-counter {
  animation: counter 0.3s ease-in-out;
}

/* Responsive typography */
@media (max-width: 640px) {
  h1 {
    font-size: 2.5rem;
    line-height: 1.2;
  }
  
  h2 {
    font-size: 2rem;
    line-height: 1.3;
  }
}

/* Enhanced button styles */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105;
}

/* Card component styles */
.card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg dark:shadow-gray-900/50 hover:shadow-2xl dark:hover:shadow-gray-900/70 transition-all duration-500 overflow-hidden hover:scale-105;
}

/* Code block styling */
.code-block {
  @apply bg-gray-900 rounded-xl overflow-hidden shadow-2xl;
}

.code-header {
  @apply bg-gray-800 px-4 py-2 flex items-center space-x-2;
}

.code-content {
  @apply p-6 text-sm text-gray-300 overflow-x-auto;
}

/* Tab styling */
.tab-button {
  @apply flex items-center space-x-2 px-6 py-4 font-semibold transition-all duration-300 border-b-2;
}

.tab-active {
  @apply text-blue-600 dark:text-blue-400 border-blue-600 dark:border-blue-400;
}

.tab-inactive {
  @apply text-gray-600 dark:text-gray-400 border-transparent hover:text-blue-600 dark:hover:text-blue-400;
}

/* Form styling */
.form-input {
  @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors;
}

.form-textarea {
  @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors resize-none;
}

/* Social link styling */
.social-link {
  @apply text-gray-400 dark:text-gray-500 transition-colors p-2 rounded-lg hover:bg-gray-800 dark:hover:bg-gray-700 hover:text-gray-200 dark:hover:text-gray-300;
}

/* Utility classes */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dark .text-gradient {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dark .bg-gradient-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
}

.backdrop-blur-custom {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .text-gray-600 {
    @apply text-gray-800 dark:text-gray-200;
  }

  .text-gray-500 {
    @apply text-gray-700 dark:text-gray-300;
  }

  .text-gray-400 {
    @apply text-gray-600 dark:text-gray-300;
  }

  .bg-gray-100 {
    @apply bg-gray-200 dark:bg-gray-700;
  }

  .bg-gray-50 {
    @apply bg-gray-100 dark:bg-gray-800;
  }
}

/* Additional dark mode improvements */
.dark .hover-lift:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
}

/* Improved code block styling for dark mode */
.dark .code-block {
  @apply bg-gray-900 border border-gray-700;
}

.dark .code-header {
  @apply bg-gray-800 border-b border-gray-700;
}

.dark .code-content {
  @apply text-gray-300;
}

/* Better selection colors */
::selection {
  background-color: #3b82f6;
  color: white;
}

.dark ::selection {
  background-color: #60a5fa;
  color: #1f2937;
}