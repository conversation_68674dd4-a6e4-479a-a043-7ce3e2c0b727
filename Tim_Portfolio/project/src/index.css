@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Code syntax highlighting improvements */
pre code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.6;
}

/* Enhanced focus states for accessibility */
button:focus,
input:focus,
textarea:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Improved card hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Loading animation for stats */
@keyframes counter {
  from {
    opacity: 0.3;
  }
  to {
    opacity: 1;
  }
}

.stat-counter {
  animation: counter 0.3s ease-in-out;
}

/* Responsive typography */
@media (max-width: 640px) {
  h1 {
    font-size: 2.5rem;
    line-height: 1.2;
  }
  
  h2 {
    font-size: 2rem;
    line-height: 1.3;
  }
}

/* Enhanced button styles */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105;
}

/* Card component styles */
.card {
  @apply bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden hover:scale-105;
}

/* Code block styling */
.code-block {
  @apply bg-gray-900 rounded-xl overflow-hidden shadow-2xl;
}

.code-header {
  @apply bg-gray-800 px-4 py-2 flex items-center space-x-2;
}

.code-content {
  @apply p-6 text-sm text-gray-300 overflow-x-auto;
}

/* Tab styling */
.tab-button {
  @apply flex items-center space-x-2 px-6 py-4 font-semibold transition-all duration-300 border-b-2;
}

.tab-active {
  @apply text-blue-600 border-blue-600;
}

.tab-inactive {
  @apply text-gray-600 border-transparent hover:text-blue-600;
}

/* Form styling */
.form-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;
}

.form-textarea {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none;
}

/* Social link styling */
.social-link {
  @apply text-gray-400 transition-colors p-2 rounded-lg hover:bg-gray-800;
}

/* Utility classes */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.backdrop-blur-custom {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}